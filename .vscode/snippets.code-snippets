// -*- jsonc -*-
{
  // Place your suite workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.

  "const [...] = useState(...)": {
    "scope": "typescript, typescriptreact",
    "prefix": "useState",
    "body": "const [${1:thing}, set${1/(.*)/${1:/capitalize}/}] = useState($3);",
  },
  "const fn = useCallback(...)": {
    "scope": "typescript, typescriptreact",
    "prefix": "useCallback",
    "body": "const ${1:func} = useCallback(($2) => {$3}, [$4]);",
  },

  // Intellisense can give you hints inside {}, so it's helpful to type the object before the
  // destructured properties.
  "const {...} = obj": {
    "scope": "typescript, typescriptreact",
    "prefix": "const",
    "body": "const {$2} = $1;",
    "description": "Destructure object",
  },

  "React component": {
    "scope": "typescriptreact",
    "prefix": "component",
    "body": "export default function ${1:$TM_FILENAME_BASE}({ children }: React.PropsWithChildren<${2:unknown}>): React.JSX.Element {\n\t$0\n}",
  },
}
