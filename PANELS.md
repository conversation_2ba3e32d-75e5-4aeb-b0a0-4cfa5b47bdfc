# Lichtblick Panels Documentation

This document provides a comprehensive guide to all panels available in Lichtblick, their architecture, and how to create new panels.

## Panel Categories

### Built-in React Panels

These panels use traditional React components and are wrapped with the `Panel` HOC:

1. **Data Source Info** - Displays information about the current data source
2. **Diagnostics - Detail (ROS)** - Detailed view of ROS diagnostic messages
3. **Diagnostics - Summary (ROS)** - Summary view of ROS diagnostic statuses
4. **Image** - Image and video visualization panel
5. **Log** - Log message viewer (RosOut)
6. **Parameters** - ROS parameter viewer and editor
7. **Plot** - Time-series data plotting panel
8. **Publish** - Publish messages to topics
9. **Raw Messages** - Raw message data viewer with JSON/tree view
10. **State Transitions** - Visualize state machine transitions
11. **Studio - Playback Performance** - Performance monitoring for playback
12. **Tab** - Container panel for organizing other panels in tabs
13. **Table** - Tabular data viewer
14. **Topic Graph** - Visualization of topic connections and message flow
15. **User Scripts** - User script editor and execution environment
16. **Variable Slider** - Global variable controls with sliders

### Built-in Extension-Style Panels

These panels use the `PanelExtensionContext` architecture internally (like built-in extensions):

1. **3D** - 3D visualization panel for rendering point clouds, meshes, and other 3D data
2. **Gauge** - Circular gauge visualization for scalar values
3. **Indicator** - Boolean indicator lights panel
4. **Map** - Map visualization panel
5. **Service Call** - Call ROS services panel
6. **Teleop** - Teleoperation controls panel

**Important**: Built-in extension-style panels **cannot use React hooks** like `useDataSourceInfo()`, `useMessagePipeline()`, or any other context-dependent hooks because they are rendered in an isolated React tree via `createSyncRoot()`. This isolation is by design to maintain API consistency with external extensions. Instead, these panels must use the `PanelExtensionContext` APIs to access data and functionality.

### External Extension Panels

Extension panels are provided by installed extensions and are dynamically loaded. They:
- Are installed through the extension system
- Use the Panel Extension API
- Are namespaced by their extension name
- Can be developed by third parties
- Are loaded dynamically through the `PanelExtensionAdapter`

The available extension panels depend on which extensions are currently installed in the user's Lichtblick instance.

## Built-in Panel Architecture

### React-Based Panel Architecture

Traditional React panels follow this flow:

1. **App Setup** (`App.tsx`/`StudioApp.tsx`)
   - Sets up providers including `PanelCatalogProvider`
   - Establishes React context for configuration and state management

2. **Panel Catalog Registration** (`PanelCatalogProvider.tsx`)
   - Calls `panels.getBuiltin(t)` to get all built-in panel definitions
   - Creates a unified catalog of both built-in and extension panels
   - Makes panels available to the layout system

3. **Panel Layout System** (`PanelLayout.tsx`)
   - Uses React Mosaic for panel layout management
   - Lazy loads panel components using `React.lazy(panelInfo.module)`
   - Creates panel instances based on `PanelInfo` definitions

4. **Panel Wrapper** (`Panel.tsx`)
   - Higher-order component that wraps all React panels
   - Manages panel lifecycle, configuration, and context
   - Provides common functionality like fullscreen, resizing, drag handles

5. **Panel Implementation**
   - Individual panel components receive `config` and `saveConfig` props
   - Implement specific visualization or interaction logic
   - Can define custom settings trees for user configuration

### Extension-Style Panel Architecture

Built-in extension-style panels follow a different flow:

1. **App Setup & Panel Catalog** (same as React panels)

2. **Extension Adapter Integration**
   - Extension-style panels use `PanelExtensionAdapter` internally
   - They have an `initPanel` function that receives `PanelExtensionContext`
   - Still wrapped with the `Panel` HOC but bridged through the adapter

3. **Context Translation**
   - `PanelExtensionAdapter` translates React props to extension context
   - Provides DOM element for rendering and extension APIs
   - Manages the extension-style lifecycle

4. **Panel Implementation**
   - Uses `createSyncRoot` to render React components into DOM
   - Access to `PanelExtensionContext` for data and settings
   - Can use either DOM manipulation or React rendering

### React Panel Structure

Each React panel follows this pattern:

```typescript
// Panel component implementation
function MyPanel({ config, saveConfig }: PanelProps<MyConfig>) {
  // Panel logic and rendering
  return <div>Panel content</div>;
}

// Static properties required by the panel system
MyPanel.panelType = "MyPanelType";
MyPanel.defaultConfig = {
  // Default configuration object
};

// Export wrapped with Panel HOC
export default Panel(MyPanel);
```

### Extension-Style Panel Structure

Built-in extension-style panels follow this pattern:

```typescript
import { useMemo } from "react";
import { useCrash } from "@lichtblick/hooks";
import { PanelExtensionContext } from "@lichtblick/suite";
import { CaptureErrorBoundary } from "@lichtblick/suite-base/components/CaptureErrorBoundary";
import Panel from "@lichtblick/suite-base/components/Panel";
import { PanelExtensionAdapter } from "@lichtblick/suite-base/components/PanelExtensionAdapter";
import { createSyncRoot } from "@lichtblick/suite-base/panels/createSyncRoot";
import { SaveConfig } from "@lichtblick/suite-base/types/panels";

import { MyPanelComponent } from "./MyPanelComponent";
import { MyPanelConfig } from "./types";

function initPanel(crash: ReturnType<typeof useCrash>, context: PanelExtensionContext) {
  return createSyncRoot(
    <CaptureErrorBoundary onError={crash}>
      <MyPanelComponent context={context} />
    </CaptureErrorBoundary>,
    context.panelElement,
  );
}

function MyPanelAdapter(props: { config: MyPanelConfig; saveConfig: SaveConfig<MyPanelConfig> }) {
  const crash = useCrash();
  const boundInitPanel = useMemo(() => initPanel.bind(undefined, crash), [crash]);

  return (
    <PanelExtensionAdapter
      config={props.config}
      saveConfig={props.saveConfig}
      initPanel={boundInitPanel}
    />
  );
}

MyPanelAdapter.panelType = "MyPanel";
MyPanelAdapter.defaultConfig = {};

export default Panel(MyPanelAdapter);
```

#### React Context Isolation

The key architectural difference is that `createSyncRoot()` creates a **completely isolated React tree** using React 18's `createRoot()`. This means:

1. **No Context Inheritance**: The panel component inside `createSyncRoot()` has no access to any React contexts from the parent application tree
2. **No React Hooks**: Cannot use `useDataSourceInfo()`, `useMessagePipeline()`, `useGlobalVariables()`, or any other context-dependent hooks
3. **PanelExtensionContext Only**: Must rely exclusively on the `context` prop (of type `PanelExtensionContext`) for all data access
4. **API Consistency**: This isolation ensures the same limitations as external extensions, maintaining API consistency

**Why This Architecture?**

This design choice ensures that built-in extension-style panels use the same APIs and limitations as external extensions, preventing internal panels from becoming dependent on Lichtblick's internal React architecture.

#### useDataSourceInfo() Alternative for Extension Panels

**The Issue**: Built-in extension panels cannot use `useDataSourceInfo()` because `useGuaranteedContext()` receives a null context due to React context isolation.

**This is a FEATURE, not a bug.**

**Solution**: Use the `PanelExtensionContext` APIs instead:

```typescript
// ❌ Cannot use this in extension-style panels
const { topics, datatypes, capabilities } = useDataSourceInfo();

// ✅ Use this instead
function MyExtensionPanel({ context }: { context: PanelExtensionContext }) {
  useEffect(() => {
    context.watch("topics");
    context.watch("services");
    context.watch("capabilities");

    context.onRender = (renderState, done) => {
      const { topics, services, capabilities } = renderState;
      // Access topics, services, and capabilities here
      done();
    };
  }, [context]);
}
```

The `PanelExtensionContext` provides equivalent functionality through:
- `renderState.topics` (equivalent to topics from useDataSourceInfo)
- `renderState.services` (equivalent to services from useDataSourceInfo)
- `renderState.capabilities` (equivalent to capabilities)
- `context.dataSourceProfile` (equivalent to dataSourceProfile)
- The datatypes are accessible through topic schemas in the render state

### Configuration System

Both panel types use the same configuration system:

1. **Panel Config Interface**
   - Each panel defines a TypeScript interface for its configuration
   - Stored in the layout's `configById` object keyed by panel ID

2. **Default Configuration**
   - Panels define `defaultConfig` static property
   - Merged with saved config and any override config
   - Applied automatically when panels are created

3. **Config Persistence**
   - Configurations are saved to layout storage (IndexedDB)
   - Updated via `saveConfig` callback provided to panels
   - Supports partial updates and function-based updates

4. **Settings UI Generation**
   - Panels can provide settings trees for automatic UI generation
   - Built using the `SettingsTree` API for consistent user experience
   - Supports validation, conditional fields, and custom input types

### Panel Context and Services

All built-in panels have access to:

- **Panel Context**: ID, type, title, config management, sibling panel operations
- **Message Pipeline**: Live data streams, topics, schemas, playback state
- **Global Variables**: Shared state across panels
- **Extensions**: Message converters, custom settings
- **Layout Actions**: Panel manipulation, fullscreen, drag operations

### Shared Resources

- **Shared Components**: Common UI elements in `packages/suite-base/src/components/`
- **Utilities**: Helper functions in `packages/suite-base/src/util/`
- **Assets**: Thumbnails and icons stored alongside panel implementations
- **Testing**: Mock providers and utilities for panel testing

## External Extension Panel Architecture

### Application Flow to Extension Panels

External extension panels have a different integration path compared to built-in panels:

1. **Extension Loading** (`ExtensionCatalogProvider.tsx`)
   - Extension loaders (`IdbExtensionLoader`) discover and load extensions
   - Extensions register panels using the `registerPanel` function
   - Panel registrations are stored in the extension catalog

2. **Panel Catalog Integration** (`PanelCatalogProvider.tsx`)
   - Extension panels are wrapped in a `PanelWrapper` component
   - Each extension panel gets a namespaced type: `${extensionName}.${panelName}`
   - Wrapped panels are added to the unified panel catalog

3. **Extension Adapter** (`PanelExtensionAdapter.tsx`)
   - Acts as a bridge between the core application and extension panels
   - Translates core panel props into `PanelExtensionContext`
   - Manages the extension panel lifecycle and rendering

4. **Extension Context** (`PanelExtensionContext`)
   - Provides a DOM element for the panel to render into
   - Exposes application APIs for data access, layout actions, and state management
   - Handles communication between extension and core application

### Extension Panel Structure

Extension panels follow this pattern:

```typescript
// Extension registration
function activate(extensionContext: ExtensionContext) {
  extensionContext.registerPanel({
    name: "MyExtensionPanel",
    initPanel: (panelContext: PanelExtensionContext) => {
      // Setup panel DOM and logic
      const panelElement = panelContext.panelElement;

      // Subscribe to data updates
      panelContext.watch("currentFrame");

      // Set up rendering
      panelContext.onRender = (renderState, done) => {
        // Render panel content
        // panelElement.innerHTML = ...
        done();
      };

      // Return cleanup function
      return () => {
        // Cleanup logic
      };
    }
  });
}
```

### Extension Capabilities

Extension panels have access to:

1. **Data Access**
   - Subscribe to topics and receive live data
   - Access to current frame, playback state, and capabilities
   - Topic metadata and schema information

2. **State Management**
   - Persistent panel state via `saveState`
   - Shared panel state across instances
   - Global variables and parameters

3. **Layout Integration**
   - Open sibling panels
   - Layout manipulation actions
   - Fullscreen support

4. **Settings Integration**
   - Custom settings trees for UI generation
   - Extension-specific configuration options
   - Integration with panel settings sidebar

### Extension Lifecycle

1. **Installation**: Extensions are installed via the marketplace or sideloaded
2. **Registration**: Extension `activate` function registers panels and other contributions
3. **Catalog Integration**: Registered panels are added to the panel catalog
4. **Panel Creation**: When a panel is added to a layout, `initPanel` is called
5. **Rendering**: Panel sets up DOM and rendering logic through `onRender`
6. **Cleanup**: When panel is removed, cleanup function is called

### Differences Between Panel Types

| Aspect | React Panels | Extension-Style Panels | External Extensions |
|--------|--------------|------------------------|-------------------|
| **Implementation** | React components | React via extension context | DOM manipulation or React |
| **Configuration** | Direct props | Extension context APIs | Extension context APIs |
| **Rendering** | React rendering | `createSyncRoot` to DOM | Direct DOM manipulation or React |
| **Data Access** | React hooks | Extension context only | Extension context only |
| **React Context Access** | ✅ Full access | ❌ Isolated (no access) | ❌ Isolated (no access) |
| **useDataSourceInfo()** | ✅ Available | ❌ Not available | ❌ Not available |
| **Lifecycle** | React lifecycle | Extension lifecycle | Extension lifecycle |
| **Development** | Standard React patterns | Hybrid React/Extension | Pure extension API |
| **Installation** | Built-in | Built-in | User-installable |

## Creating New Panels

### How to Create a New React Panel

Creating a new React panel involves several steps:

#### 1. Create Panel Directory

Create a new directory under `packages/suite-base/src/panels/`:

```
packages/suite-base/src/panels/MyNewPanel/
├── index.tsx           # Main panel component
├── types.ts           # TypeScript type definitions
├── settings.ts        # Panel settings configuration (optional)
├── thumbnail.png      # Panel thumbnail for catalog
└── MyNewPanel.stories.tsx  # Storybook stories (optional)
```

#### 2. Implement Panel Component

Create `index.tsx`:

```typescript
import React from "react";
import { useMessagePipeline } from "@lichtblick/suite-base/components/MessagePipeline";
import Panel from "@lichtblick/suite-base/components/Panel";
import { SaveConfig } from "@lichtblick/suite-base/types/panels";

// Define your configuration interface
export interface MyNewPanelConfig {
  topicPath: string;
  someOption: boolean;
}

// Define panel component
function MyNewPanel({ config, saveConfig }: {
  config: MyNewPanelConfig;
  saveConfig: SaveConfig<MyNewPanelConfig>;
}) {
  // Access message pipeline for live data
  const topics = useMessagePipeline((state) => state.sortedTopics);

  // Panel implementation
  return (
    <div style={{ padding: "1rem" }}>
      <h3>My New Panel</h3>
      <p>Topic: {config.topicPath}</p>
      <button
        onClick={() => saveConfig({ ...config, someOption: !config.someOption })}
      >
        Toggle Option: {config.someOption ? "On" : "Off"}
      </button>
    </div>
  );
}

// Required static properties
MyNewPanel.panelType = "MyNewPanel";
MyNewPanel.defaultConfig: MyNewPanelConfig = {
  topicPath: "",
  someOption: false,
};

// Export wrapped with Panel HOC
export default Panel(MyNewPanel);
```

#### 3. Add Panel to Registry

Update `packages/suite-base/src/panels/index.ts`:

```typescript
// Add thumbnail import
import myNewPanelThumbnail from "./MyNewPanel/thumbnail.png";

// Add to getBuiltin function
export const getBuiltin: (t: TFunction<"panels">) => PanelInfo[] = (t) => [
  // ... existing panels ...
  {
    title: t("myNewPanel"),
    type: "MyNewPanel",
    description: t("myNewPanelDescription"),
    thumbnail: myNewPanelThumbnail,
    module: async () => await import("./MyNewPanel"),
  },
];
```

#### 4. Add Translations

Add translations to the relevant files in `packages/suite-base/src/i18n/*/panels.ts`:

```typescript
// en/panels.ts
export const panels = {
  // ... existing translations ...
  myNewPanel: "My New Panel",
  myNewPanelDescription: "Description of what my panel does",
};
```

### How to Create a New Built-in Extension-Style Panel

Creating a built-in extension-style panel:

#### 1. Create Panel Directory (same as React panel)

#### 2. Implement Extension-Style Panel

Create `index.tsx`:

```typescript
import { useMemo } from "react";
import { useCrash } from "@lichtblick/hooks";
import { PanelExtensionContext } from "@lichtblick/suite";
import { CaptureErrorBoundary } from "@lichtblick/suite-base/components/CaptureErrorBoundary";
import Panel from "@lichtblick/suite-base/components/Panel";
import { PanelExtensionAdapter } from "@lichtblick/suite-base/components/PanelExtensionAdapter";
import { createSyncRoot } from "@lichtblick/suite-base/panels/createSyncRoot";
import { SaveConfig } from "@lichtblick/suite-base/types/panels";

import { MyPanelComponent } from "./MyPanelComponent";
import { MyPanelConfig } from "./types";

function initPanel(crash: ReturnType<typeof useCrash>, context: PanelExtensionContext) {
  return createSyncRoot(
    <CaptureErrorBoundary onError={crash}>
      <MyPanelComponent context={context} />
    </CaptureErrorBoundary>,
    context.panelElement,
  );
}

function MyPanelAdapter(props: { config: MyPanelConfig; saveConfig: SaveConfig<MyPanelConfig> }) {
  const crash = useCrash();
  const boundInitPanel = useMemo(() => initPanel.bind(undefined, crash), [crash]);

  return (
    <PanelExtensionAdapter
      config={props.config}
      saveConfig={props.saveConfig}
      initPanel={boundInitPanel}
    />
  );
}

MyPanelAdapter.panelType = "MyPanel";
MyPanelAdapter.defaultConfig = {};

export default Panel(MyPanelAdapter);
```

#### 3. Add Panel to Registry (same as React panel)

### How to Create a New External Extension Panel

Creating an external extension panel requires setting up an extension project:

#### 1. Extension Project Structure

Create a new directory for your extension:

```
my-lichtblick-extension/
├── package.json
├── tsconfig.json
├── webpack.config.js
├── src/
│   ├── index.ts         # Extension entry point
│   └── MyPanel.ts       # Panel implementation
└── dist/                # Built extension files
```

#### 2. Setup Package Configuration

Create `package.json`:

```json
{
  "name": "my-lichtblick-extension",
  "version": "1.0.0",
  "description": "My custom Lichtblick extension",
  "main": "dist/extension.js",
  "files": ["dist"],
  "devDependencies": {
    "@lichtblick/suite": "latest",
    "typescript": "^4.0.0",
    "webpack": "^5.0.0",
    "webpack-cli": "^4.0.0"
  }
}
```

#### 3. Implement Extension

Create `src/index.ts`:

```typescript
import { ExtensionContext } from "@lichtblick/suite";

export function activate(extensionContext: ExtensionContext) {
  extensionContext.registerPanel({
    name: "MyExtensionPanel",
    initPanel: (panelContext) => {
      const { panelElement } = panelContext;

      // Set up your panel UI
      panelElement.innerHTML = `
        <div style="padding: 1rem;">
          <h3>My Extension Panel</h3>
          <div id="content">Loading...</div>
        </div>
      `;

      const contentDiv = panelElement.querySelector("#content");

      // Subscribe to render updates
      panelContext.watch("currentFrame");

      // Handle render events
      panelContext.onRender = (renderState, done) => {
        if (contentDiv) {
          const frame = renderState.currentFrame;
          contentDiv.innerHTML = `
            <p>Current time: ${frame?.receiveTime ? new Date(frame.receiveTime).toISOString() : 'N/A'}</p>
            <p>Active topics: ${Object.keys(renderState.topics || {}).length}</p>
          `;
        }
        done();
      };

      // Set up panel settings
      panelContext.updatePanelSettingsEditor({
        actionHandler: (action) => {
          // Handle settings changes
          console.log("Settings action:", action);
        },
        nodes: {
          general: {
            label: "General Settings",
            fields: {
              refreshRate: {
                label: "Refresh Rate",
                input: "number",
                value: 30,
                min: 1,
                max: 60,
              },
            },
          },
        },
      });

      // Return cleanup function
      return () => {
        console.log("Panel cleanup");
      };
    },
  });
}
```

#### 4. Build Configuration

Create `webpack.config.js`:

```javascript
const path = require("path");

module.exports = {
  mode: "production",
  entry: "./src/index.ts",
  output: {
    path: path.resolve(__dirname, "dist"),
    filename: "extension.js",
    library: {
      type: "commonjs2",
    },
  },
  resolve: {
    extensions: [".ts", ".tsx", ".js"],
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
    ],
  },
  externals: {
    "@lichtblick/suite": "@lichtblick/suite",
  },
};
```

#### 5. TypeScript Configuration

Create `tsconfig.json`:

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020", "DOM"],
    "declaration": true,
    "outDir": "./dist",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

#### 6. Build and Install

Build your extension:

```bash
npm install
npx webpack
```

Install in Lichtblick:
1. Open Lichtblick
2. Go to Extensions panel
3. Click "Install from local file"
4. Select your built extension file from the `dist` directory

### Best Practices

#### For React Panels

1. **Follow existing patterns**: Study existing React panels for consistent architecture
2. **TypeScript**: Always use TypeScript with proper type definitions
3. **Performance**: Use React optimization techniques (memo, useMemo, useCallback)
4. **Testing**: Write unit tests and Storybook stories
5. **Accessibility**: Follow WCAG guidelines for keyboard navigation and screen readers
6. **Error handling**: Implement proper error boundaries and validation
7. **Settings**: Provide intuitive settings for user customization

#### For Extension-Style Panels

1. **Choose the right approach**: Use extension-style for complex data processing or when you need the extension context APIs
2. **React integration**: Use `createSyncRoot` to render React components in extension context
3. **Context isolation awareness**: Remember that you cannot use React hooks that depend on contexts (like `useDataSourceInfo()`, `useMessagePipeline()`, etc.)
4. **Use PanelExtensionContext APIs**: Access all data through the `context` parameter instead of React hooks
5. **Error boundaries**: Always wrap your React components in error boundaries
6. **Resource cleanup**: Ensure proper cleanup when the extension unmounts

#### For External Extension Panels

1. **Resource management**: Always clean up event listeners and intervals
2. **Performance**: Minimize DOM manipulation and use efficient rendering
3. **Error handling**: Handle errors gracefully without crashing the app
4. **State management**: Use the provided APIs for state persistence
5. **Security**: Never execute arbitrary code or access sensitive data
6. **Documentation**: Provide clear documentation for your extension users
7. **Versioning**: Use semantic versioning for compatibility
