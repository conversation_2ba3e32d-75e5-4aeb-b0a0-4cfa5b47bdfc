// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

import Clock from "@mui/icons-material/AccessTime";
import Add from "@mui/icons-material/Add";
import Addchart from "@mui/icons-material/Addchart";
import AutoAwesome from "@mui/icons-material/AutoAwesome";
import Points from "@mui/icons-material/BlurOn";
import Check from "@mui/icons-material/Check";
import Circle from "@mui/icons-material/Circle";
import Clear from "@mui/icons-material/Clear";
import Delete from "@mui/icons-material/Delete";
import Walk from "@mui/icons-material/DirectionsWalk";
import Flag from "@mui/icons-material/Flag";
import Folder from "@mui/icons-material/Folder";
import FolderOpen from "@mui/icons-material/FolderOpen";
import Grid from "@mui/icons-material/GridOn";
import Hive from "@mui/icons-material/HiveOutlined";
import Shapes from "@mui/icons-material/Interests";
import World from "@mui/icons-material/Language";
import Background from "@mui/icons-material/Layers";
import Map from "@mui/icons-material/Map";
import MoveDown from "@mui/icons-material/MoveDown";
import MoveUp from "@mui/icons-material/MoveUp";
import NorthWest from "@mui/icons-material/NorthWest";
import NoteFilled from "@mui/icons-material/Note";
import Note from "@mui/icons-material/NoteOutlined";
import Move from "@mui/icons-material/OpenWith";
import Camera from "@mui/icons-material/PhotoCamera";
import PrecisionManufacturing from "@mui/icons-material/PrecisionManufacturing";
import Radar from "@mui/icons-material/Radar";
import Settings from "@mui/icons-material/Settings";
import Share from "@mui/icons-material/Share";
import SouthEast from "@mui/icons-material/SouthEast";
import Star from "@mui/icons-material/StarOutline";
import Timeline from "@mui/icons-material/Timeline";
import Topic from "@mui/icons-material/Topic";
import Collapse from "@mui/icons-material/UnfoldLess";
import Expand from "@mui/icons-material/UnfoldMore";
import Cells from "@mui/icons-material/ViewComfy";
import Cube from "@mui/icons-material/ViewInAr";
import ImageProjection from "@mui/icons-material/Vrpano";

import { SettingsIcon } from "@lichtblick/suite";

const icons: Record<SettingsIcon, typeof Add> = {
  Add,
  Addchart,
  AutoAwesome,
  Background,
  Camera,
  Cells,
  Check,
  Circle,
  Clear,
  Clock,
  Collapse,
  Cube,
  Delete,
  Expand,
  Flag,
  Folder,
  FolderOpen,
  Grid,
  Hive,
  ImageProjection,
  Map,
  Move,
  MoveDown,
  MoveUp,
  NorthWest,
  Note,
  NoteFilled,
  Points,
  PrecisionManufacturing,
  Radar,
  Settings,
  Shapes,
  Share,
  SouthEast,
  Star,
  Timeline,
  Topic,
  Walk,
  World,
};

export { icons };
