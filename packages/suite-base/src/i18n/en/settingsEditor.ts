// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const settingsEditor = {
  clearSearch: "Clear search",
  filterList: "Filter list",
  filterListHelp: "Filter list by visibility",
  general: "General",
  hideAll: "Hide All",
  labels: "Labels",
  listAll: "List all",
  listInvisible: "List invisible",
  listVisible: "List visible",
  searchPanelSettings: "Search panel settings…",
  showAll: "Show All",
  title: "Title",
};
