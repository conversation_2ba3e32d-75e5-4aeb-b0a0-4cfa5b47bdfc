// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

export const extensionsSettings = {
  noExtensionsFound: "No extensions found",
  noExtensionsAvailable: "No extensions available",
  failedToRetrieveMarketplaceExtensions:
    "Failed to retrieve the list of available marketplace extensions",
  checkInternetConnection: "Check your internet connection and try again.",
  searchExtensions: "Search extensions...",
  available: "Available",
};
