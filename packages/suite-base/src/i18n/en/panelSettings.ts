// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const panelSettings = {
  currentSettingsPanelName: "{{title}} panel settings",
  importOrExportSettings: "Import/export settings",
  importOrExportSettingsWithEllipsis: "Import/export settings…",
  loadingPanelSettings: "Loading panel settings…",
  panelDoesNotHaveSettings: "This panel does not have any settings",
  panelName: "{{title}} panel",
  panelSettings: "Panel settings",
  resetToDefaults: "Reset to defaults",
  selectAPanelToEditItsSettings: "Select a panel to edit its settings.",
  unknown: "Unknown",
};
