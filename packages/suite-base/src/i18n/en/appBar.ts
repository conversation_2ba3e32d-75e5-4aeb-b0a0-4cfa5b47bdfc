// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const appBar = {
  about: "About",
  addPanel: "Add panel",
  documentation: "Documentation",
  exploreSampleData: "Explore sample data",
  exportLayoutToFile: "Export layout to file…",
  extensions: "Extensions",
  file: "File",
  help: "Help",
  hideLeftSidebar: "Hide left sidebar",
  hideRightSidebar: "Hide right sidebar",
  importLayoutFromFile: "Import layout from file…",
  noDataSource: "No data source",
  open: "Open…",
  openConnection: "Open connection…",
  openDataSources: "Open data sources",
  openLocalFiles: "Open local file(s)…",
  recentDataSources: "Recent data sources",
  recentlyViewed: "Recently viewed",
  settings: "Visualization settings",
  showLeftSidebar: "Show left sidebar",
  showRightSidebar: "Show right sidebar",
  signIn: "Sign in",
  signOut: "Sign out",
  unknown: "unknown",
  userProfile: "User profile",
  view: "View",
  viewData: "View data",
};
