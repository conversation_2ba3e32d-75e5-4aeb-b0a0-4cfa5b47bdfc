// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const addPanel = {
  addPanel: "Add panel",
  noLayoutSelected: "<selectLayoutLink>Select a layout</selectLayoutLink> to get started!",
  noPanelsMatchSearchCriteria: "No panels match search criteria.",
  searchPanels: "Search panels",
  selectPanelToAddToLayout: "Select a panel below to add it to your layout.",
};
