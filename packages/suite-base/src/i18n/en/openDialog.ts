// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const openDialog = {
  canBeShared: "Share data files, visualization layouts, and custom extensions with teammates",
  collaborateTitle: "Accelerate development with Lichtblick Data Platform",
  convenientWebInterface:
    "Use a convenient web interface to tag, search, and retrieve data at lightning speed",
  createAFreeAccount: "Create a free account",
  dontShowThisAgain: "Don't show this again on startup",
  exploreSampleData: "Explore sample data",
  viewDocumentation: "View documentation",
  learnMore: "Learn more",
  needHelp: "Need help?",
  needHelpDescription: "View our documentation, or check out the tutorials on the Foxglove blog.",
  newToLichtblick: "New to Lichtblick?",
  newToLichtblickDescription:
    "Start by exploring a sample dataset or checking out our documentation.",
  openAGitHubIssue: "Open a GitHub issue",
  openConnection: "Open connection",
  openConnectionDescription: "Connect to a live robot or server.",
  openDataSource: "Open data source",
  openLocalFiles: "Open local file(s)...",
  openLocalFileDescription: "Visualize data directly from your local filesystem.",
  openUrl: "Upload and share data",
  openUrlDescription: "Use Foxglove Data Platform to share data with your team.",
  recentDataSources: "Recent data sources",
  secureStorageOfData: "Securely store petabytes of ROS or custom data",
  seeTutorials: "See tutorials",
  shareLayouts: "Share layouts",
  signIn: "Sign in",
  startCollaborating: "Start collaborating with your Foxglove organization",
  startCollaboratingDescription:
    "Make the most of your Foxglove account – whether you want to dive deep on your data or share tools with your teammates.",
  uploadToDataPlatform: "Upload to Data Platform",
};
