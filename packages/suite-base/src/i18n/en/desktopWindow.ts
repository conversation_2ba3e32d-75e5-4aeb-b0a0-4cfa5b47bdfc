// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const desktopWindow = {
  advanced: "Advanced",
  checkForUpdates: "Check for Updates…",
  edit: "Edit",
  file: "File",
  inspectSharedWorker: "Inspect Shared Worker…",
  newWindow: "New Window",
  noSharedWorkers: "No Shared Workers",
  settings: "Settings…",
  view: "View",
};
