// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const plot = {
  accumulatedPath: "Path (accumulated)",
  addSeries: "Add series",
  clickToAddASeries: "Click to add a series",
  color: "Color",
  currentPath: "Path (current)",
  deleteSeries: "Delete series",
  floating: "Floating",
  general: "General",
  headerStamp: "Header Stamp",
  hidden: "Hidden",
  index: "Index",
  label: "Label",
  left: "Left",
  legend: "Legend",
  lineSize: "Line size",
  max: "Max",
  maxXError: "X max must be greater than X min.",
  maxYError: "Y max must be greater than Y min.",
  messagePath: "Message path",
  min: "Min",
  position: "Position",
  receiveTime: "Receive Time",
  resetView: "Reset view",
  secondsRange: "Range (seconds)",
  series: "Series",
  showLabels: "Show labels",
  showLine: "Show lines",
  showValues: "Show values",
  syncWithOtherPlots: "Sync with other plots",
  timestamp: "Timestamp",
  top: "Top",
  value: "Value",
  xAxis: "X Axis",
  yAxis: "Y Axis",
};
