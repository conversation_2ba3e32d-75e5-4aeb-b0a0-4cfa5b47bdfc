// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const stateTransitions = {
  addSeriesButton: "Click to add a series",
  labels: {
    addSeries: "Add series",
    deleteSeries: "Delete series",
    general: "General",
    helpGeneral: "Display a point for every state transition message",
    label: "Label",
    messagePath: "Message path",
    series: "Series",
    showPoints: "Show points",
    sync: "Sync with other plots",
    timestamp: "Timestamp",
    timestampHeaderStamp: "Header Stamp",
    timestampReceiveTime: "Receive Time",
  },
  max: "Max",
  maxXError: "X max must be greater than X min.",
  min: "Min",
  pathErrorMessage: "This path resolves to more than one value",
  secondsRange: "Range (seconds)",
  xAxis: "X Axis",
};
