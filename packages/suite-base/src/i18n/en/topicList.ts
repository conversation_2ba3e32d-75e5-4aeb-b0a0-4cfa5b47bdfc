// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const topicList = {
  anErrorOccurred: "An error occurred",
  clearFilter: "Clear filter",
  copyMessagePath: "Copy message path",
  copyMessagePaths: "Copy selected message paths",
  copySchemaName: "Copy schema name",
  copyTopicName: "Copy topic name",
  copyTopicNames: "Copy selected topic names",
  noDataSourceSelected: "No data source selected",
  noTopicsAvailable: "No topics available. ",
  noTopicsOrDatatypesMatching: "No topics or datatypes matching",
  searchBarPlaceholder: "Filter by topic or schema name…",
  waitingForConnection: "Waiting for connection",
  waitingForData: "Waiting for data…",
};
