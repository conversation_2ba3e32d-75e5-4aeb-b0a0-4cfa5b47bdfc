// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const panels = {
  "3D": "3D",
  "3DPanelDescription": "Display markers, camera images, meshes, URDFs, and more in a 3D scene.",
  callService: "Service Call",
  callServiceDescription: "Call a service and view the service call result",
  dataSourceInfo: "Data Source Info",
  dataSourceInfoDescription: "View details like topics and timestamps for the current data source.",
  gauge: "Gauge",
  gaugeDescription: "Display a colored gauge based on a continuous value.",
  image: "Image",
  imageDescription: "Display annotated images.",
  indicator: "Indicator",
  indicatorDescription: "Display a colored and/or textual indicator based on a threshold value.",
  log: "Log",
  logDescription: "Display logs by node and severity level.",
  map: "Map",
  mapDescription: "Display points on a map.",
  parameters: "Parameters",
  parametersDescription: "Read and set parameters for a data source.",
  plot: "Plot",
  plotDescription: "Plot numerical values over time or other values.",
  pieChart: "Pie Chart",
  pieChartDescription: "Display a pie chart based on a continuous value.",
  publish: "Publish",
  publishDescription: "Publish messages to the data source (live connections only).",
  rawMessages: "Raw Messages",
  rawMessagesDescription: "Inspect topic messages.",
  ROSDiagnosticsDetail: "Diagnostics – Detail (ROS)",
  ROSDiagnosticsDetailDescription:
    "Display ROS DiagnosticArray messages for a specific hardware_id.",
  ROSDiagnosticSummary: "Diagnostics – Summary (ROS)",
  ROSDiagnosticSummaryDescription: "Display a summary of all ROS DiagnosticArray messages.",
  stateTransitions: "State Transitions",
  stateTransitionsDescription: "Track when values change over time.",
  studioPlaybackPerformance: "Studio - Playback Performance",
  studioPlaybackPerformanceDescription:
    "Display playback and data-streaming performance statistics.",
  tab: "Tab",
  tabDescription: "Group panels together in a tabbed interface.",
  table: "Table",
  tableDescription: "Display topic messages in a tabular format.",
  teleop: "Teleop",
  teleopDescription: "Teleoperate a robot over a live connection.",
  topicGraph: "Topic Graph",
  topicGraphDescription: "Display a graph of active nodes, topics, and services.",
  userScripts: "User Scripts",
  userScriptsDescription:
    "Write custom data transformations in TypeScript. Previously known as Node Playground.",
  variableSlider: "Variable Slider",
  variableSliderDescription: "Update numerical variable values for a layout.",
};
