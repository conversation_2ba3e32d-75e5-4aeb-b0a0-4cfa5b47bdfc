// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

export const appSettings = {
  about: "About",
  advanced: "Advanced",
  askEachTime: "Ask each time",
  colorScheme: "Color scheme",
  dark: "Dark",
  debugModeDescription: "Enable panels and features for debugging Lichtblick",
  desktopApp: "Desktop app",
  displayTimestampsIn: "Display timestamps in",
  experimentalFeatures: "Experimental features",
  experimentalFeaturesDescription: "These features are unstable and not recommended for daily use.",
  extensions: "Extensions",
  followSystem: "Follow system",
  general: "General",
  language: "Language",
  layoutDebugging: "Layout debugging",
  layoutDebuggingDescription: "Show extra controls for developing and debugging layout storage.",
  light: "Light",
  messageRate: "Message rate",
  stepSize: "Step size",
  memoryUseIndicator: "Memory use indicator",
  memoryUseIndicatorDescription: "Show the app memory use in the sidebar.",
  syncLichtblickInstances: "Sync Lichtblick instances",
  syncLichtblickInstancesDescription:
    "Activates the button in the right lower corner of the application to sync Lichtblick instances opened.",
  noExperimentalFeatures: "Currently there are no experimental features.",
  openLinksIn: "Open links in",
  ros: "ROS",
  settings: "Settings",
  timestampFormat: "Timestamp format",
  webApp: "Web app",
};
