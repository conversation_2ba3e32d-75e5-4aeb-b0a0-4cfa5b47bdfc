// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

import * as THREE from "three";

import { RenderableMarker } from "./RenderableMarker";
import { makeStandardMaterial } from "./materials";
import type { IRenderer } from "../../IRenderer";
import { rgbToThreeColor } from "../../color";
import { cylinderSubdivisions, DetailLevel } from "../../lod";
import { Marker } from "../../ros";

export class RenderableCone extends RenderableMarker {
  #mesh: THREE.Mesh<THREE.ConeGeometry, THREE.MeshStandardMaterial>;
  #outline: THREE.LineSegments;

  public constructor(
    topic: string,
    marker: Marker,
    receiveTime: bigint | undefined,
    renderer: IRenderer,
  ) {
    super(topic, marker, receiveTime, renderer);

    // Cone mesh
    const material = makeStandardMaterial(marker.color);
    const coneGeometry = renderer.sharedGeometry.getGeometry(
      `${this.constructor.name}-cone-${renderer.maxLod}`,
      () => createGeometry(renderer.maxLod),
    );
    this.#mesh = new THREE.Mesh(coneGeometry, material);
    this.#mesh.castShadow = true;
    this.#mesh.receiveShadow = true;
    this.add(this.#mesh);

    // Cone outline
    const edgesGeometry = renderer.sharedGeometry.getGeometry(
      `${this.constructor.name}-edges-${renderer.maxLod}`,
      () => createEdgesGeometry(coneGeometry),
    );
    this.#outline = new THREE.LineSegments(edgesGeometry, renderer.outlineMaterial);
    this.#outline.userData.picking = false;
    this.#mesh.add(this.#outline);

    this.update(marker, receiveTime);
  }

  public override dispose(): void {
    this.#mesh.material.dispose();
  }

  public override update(newMarker: Marker, receiveTime: bigint | undefined): void {
    super.update(newMarker, receiveTime);
    const marker = this.userData.marker;

    const transparent = marker.color.a < 1;
    if (transparent !== this.#mesh.material.transparent) {
      this.#mesh.material.transparent = transparent;
      this.#mesh.material.depthWrite = !transparent;
      this.#mesh.material.needsUpdate = true;
    }

    this.#outline.visible = this.getSettings()?.showOutlines ?? true;

    rgbToThreeColor(this.#mesh.material.color, marker.color);
    this.#mesh.material.opacity = marker.color.a;

    this.scale.set(marker.scale.x, marker.scale.y, marker.scale.z);
  }
}

function createGeometry(lod: DetailLevel): THREE.ConeGeometry {
  const subdivisions = cylinderSubdivisions(lod);
  const coneGeometry = new THREE.ConeGeometry(0.5, 1, subdivisions);
  coneGeometry.rotateX(Math.PI / 2); // Make the cone geometry stand upright (z-axis as height)
  coneGeometry.computeBoundingSphere();
  return coneGeometry;
}

function createEdgesGeometry(geometry: THREE.ConeGeometry): THREE.EdgesGeometry {
  const coneEdgesGeometry = new THREE.EdgesGeometry(geometry, 40);
  coneEdgesGeometry.computeBoundingSphere();
  return coneEdgesGeometry;
}
