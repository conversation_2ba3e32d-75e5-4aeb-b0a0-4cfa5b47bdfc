// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/

import * as THREE from "three";

import { RenderableMarker } from "./RenderableMarker";
import { makeStandardMaterial } from "./materials";
import type { IRenderer } from "../../IRenderer";
import { rgbToThreeColor } from "../../color";
import { Marker } from "../../ros";

export class RenderablePlane extends RenderableMarker {
  #mesh: THREE.Mesh<THREE.PlaneGeometry, THREE.MeshStandardMaterial>;
  #outline: THREE.LineSegments;

  public constructor(
    topic: string,
    marker: Marker,
    receiveTime: bigint | undefined,
    renderer: <PERSON><PERSON><PERSON><PERSON>,
  ) {
    super(topic, marker, receiveTime, renderer);

    // Plane mesh
    const planeGeometry = this.renderer.sharedGeometry.getGeometry(
      `${this.constructor.name}-plane`,
      createGeometry,
    );
    const planeEdgesGeometry = this.renderer.sharedGeometry.getGeometry(
      `${this.constructor.name}-plane-edges`,
      () => createEdgesGeometry(planeGeometry),
    );
    const material = makeStandardMaterial(marker.color);
    material.side = THREE.DoubleSide; // Make the plane visible from both sides
    this.#mesh = new THREE.Mesh(planeGeometry, material);
    this.#mesh.castShadow = true;
    this.#mesh.receiveShadow = true;
    this.add(this.#mesh);

    // Plane outline
    this.#outline = new THREE.LineSegments(planeEdgesGeometry, renderer.outlineMaterial);
    this.#outline.userData.picking = false;
    this.#mesh.add(this.#outline);

    this.update(marker, receiveTime);
  }

  public override dispose(): void {
    this.#mesh.material.dispose();
  }

  public override update(newMarker: Marker, receiveTime: bigint | undefined): void {
    super.update(newMarker, receiveTime);
    const marker = this.userData.marker;

    const transparent = marker.color.a < 1;
    if (transparent !== this.#mesh.material.transparent) {
      this.#mesh.material.transparent = transparent;
      this.#mesh.material.depthWrite = !transparent;
      this.#mesh.material.needsUpdate = true;
    }

    this.#outline.visible = this.getSettings()?.showOutlines ?? true;

    rgbToThreeColor(this.#mesh.material.color, marker.color);
    this.#mesh.material.opacity = marker.color.a;

    this.scale.set(marker.scale.x, marker.scale.y, marker.scale.z);
  }
}

export function createGeometry(): THREE.PlaneGeometry {
  const planeGeometry = new THREE.PlaneGeometry(1, 1);
  planeGeometry.computeBoundingSphere();
  return planeGeometry;
}

function createEdgesGeometry(planeGeometry: THREE.PlaneGeometry): THREE.EdgesGeometry {
  const planeEdgesGeometry = new THREE.EdgesGeometry(planeGeometry, 40);
  planeEdgesGeometry.computeBoundingSphere();
  return planeEdgesGeometry;
}
