// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// SPDX-FileCopyrightText: Copyright (C) 2025 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

export const SUPPORTED_DATA_TYPES = [
  "int8",
  "uint8",
  "int16",
  "uint16",
  "int32",
  "uint32",
  "float32",
  "float64",
  "string",
];

export const DEFAULT_CONFIG = {
  path: "",
  title: "Pie Chart",
  legend1: "Legend 1",
  legend2: "Legend 2",
  legend3: "Legend 3",
  legend4: "Legend 4",
  legend5: "Legend 5",
  legend6: "Legend 6",
  legend7: "Legend 7",
  legend8: "Legend 8",
  legend9: "Legend 9",
  legend10: "Legend 10",
};
