// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// SPDX-FileCopyrightText: Copyright (C) 2025 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

import type { PieChartState } from "../types";

export function handleSeek(state: PieChartState): PieChartState {
  return {
    ...state,
    latestMessage: undefined,
    latestMatchingQueriedData: undefined,
    error: undefined,
  };
}
