// SPDX-FileCopyrightText: Copyright (C) 2023-2025 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)<<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

// SPDX-FileCopyrightText: Copyright (C) 2024-2025 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: Copyright (C) 2025 <PERSON><PERSON>yuki Honda <<EMAIL>>
// SPDX-License-Identifier: MPL-2.0

import { MessagePath } from "@lichtblick/message-path";
import { MessageEvent } from "@lichtblick/suite";

export type PieChartConfig = {
  path: string;
  title: string;
} & {
  [key in `legend${number}`]?: string;
};

export type PieChartState = {
  path: string;
  parsedPath: MessagePath | undefined;
  latestMessage: MessageEvent | undefined;
  latestMatchingQueriedData: unknown;
  error: Error | undefined;
  pathParseError: string | undefined;
};

export type PieChartAction =
  | { type: "frame"; messages: readonly MessageEvent[] }
  | { type: "path"; path: string }
  | { type: "seek" };

export type PieChartDatum = {
  name: string;
  value: number;
  color: string;
};
