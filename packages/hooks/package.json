{"name": "@lichtblick/hooks", "license": "MPL-2.0", "private": true, "repository": {"type": "git", "url": "https://github.com/lichtblick-suite/lichtblick.git"}, "author": {"name": "Lichtblick", "email": "<EMAIL>"}, "homepage": "https://github.com/lichtblick-suite", "main": "./src/index.ts", "files": ["dist", "src"], "scripts": {"prepack": "tsc -b"}, "devDependencies": {"@lichtblick/tsconfig": "1.0.0", "@testing-library/react": "16.0.0", "@types/foxglove__web": "workspace:*", "@types/lodash-es": "^4", "typescript": "5.3.3"}, "dependencies": {"@lichtblick/log": "workspace:*", "lodash-es": "4.17.21", "shallowequal": "1.1.0"}, "peerDependencies": {"react": "^18.3.1"}}