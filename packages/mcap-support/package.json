{"name": "@lichtblick/mcap-support", "description": "Common schema and message parsing logic for use with MCAP and related protocols", "license": "MPL-2.0", "private": true, "repository": {"type": "git", "url": "https://github.com/lichtblick-suite/lichtblick.git"}, "author": {"name": "Lichtblick", "email": "<EMAIL>"}, "homepage": "https://github.com/lichtblick-suite", "main": "./src/index.ts", "files": ["dist", "src"], "scripts": {"prepack": "tsc -b"}, "devDependencies": {"@lichtblick/tsconfig": "1.0.0", "@types/protobufjs": "workspace:*", "typescript": "5.3.3"}, "dependencies": {"@foxglove/schemas": "1.6.6", "@lichtblick/message-definition": "1.0.0", "@lichtblick/omgidl-parser": "1.0.0", "@lichtblick/omgidl-serialization": "1.0.0", "@lichtblick/ros2idl-parser": "1.0.0", "@lichtblick/rosmsg": "1.0.0", "@lichtblick/rosmsg-serialization": "1.0.0", "@lichtblick/rosmsg2-serialization": "1.0.0", "@lichtblick/wasm-bz2": "1.0.0", "@lichtblick/wasm-lz4": "1.0.0", "@lichtblick/wasm-zstd": "1.0.0", "@mcap/core": "2.1.6", "@protobufjs/base64": "1.1.2", "flatbuffers": "24.3.25", "flatbuffers_reflection": "0.0.7", "protobufjs": "patch:protobufjs@7.2.5#../../patches/protobufjs.patch"}}