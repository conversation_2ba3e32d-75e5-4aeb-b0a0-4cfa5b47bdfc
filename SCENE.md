# Scene Extensions In-Depth

This document provides a detailed description of how to extend the 3D panel with custom scene content, focusing on the mechanics of rendering, transformation, and data flow.

## Core Architecture

The rendering architecture is designed to be modular and extensible, separating the core rendering engine from the logic that provides scene content.

### `IRenderer` and `Renderer`: The Engine and its API

*   **`IRenderer.ts`**: This file defines the `IRenderer` interface. It acts as a **contract** or **API** that `SceneExtension`s must use to interact with the main rendering engine. It provides a stable, controlled interface, abstracting away the complex implementation details of the `Renderer`. Key aspects of this API include:
    *   **State Access**: Read-only access to fundamental state like `config`, `topics`, `currentTime`, `transformTree`, and `colorScheme`.
    *   **Shared Services**: Access to shared, expensive resources like `modelCache`, `labelPool`, and `markerPool`. This promotes resource reuse and efficiency.
    *   **State Modification**: A set of methods (`updateConfig`, `addTransform`, etc.) that are the only approved ways for an extension to request a change to the renderer's state.
    *   **Event-Driven-Architecture**: An event emitter (`addListener`, `removeListener`) that allows extensions to react to state changes (e.g., `startFrame`, `selectedRenderable`) without needing to poll for them.

*   **`Renderer.ts`**: This is the concrete **implementation** of the `IRenderer` interface. It is the core component of the 3D panel, orchestrating the entire rendering lifecycle.
    *   **How it Works**: It initializes and manages the core `THREE.WebGLRenderer`, `THREE.Scene`, camera, and lighting. It owns the `TransformTree` and is responsible for populating it with data from TF messages. The `animationFrame` method in this class drives the entire render loop.
    *   **Architectural Rationale**: This centralized model provides a single source of truth and control. By managing the core rendering components, it ensures a consistent and synchronized scene. `SceneExtension`s are freed from the complexities of WebGL, input handling, and render pass management, allowing them to focus purely on the logic of *what* they want to render, not *how* to render it.

### `Renderable` and `userData`: The Atomic Unit of the Scene

A `Renderable` is more than just a `THREE.Object3D`; it is an object semantically linked to the rendering engine through its `userData` property. This structured data is not optional—it is essential for the object to be correctly managed.

*   **`BaseUserData` Breakdown**:
    *   `frameId: string`: **The spatial anchor for the object.** It specifies the coordinate frame in which the object's `pose` is defined. Without this, the pose is meaningless.
    *   `pose: Pose`: The object's local position and orientation *relative to its `frameId`*.
    *   `messageTime: bigint`: The timestamp from the source message's header (`header.stamp`). This is critical for temporal accuracy, allowing the `TransformTree` to look up the exact transform at that specific moment in time. This is used when an object is not "frame-locked."
    *   `receiveTime: bigint`: The time the message was received by the panel, used for ordering and processing.
    *   `settingsPath: ReadonlyArray<string>`: A direct link to this object's configuration in the settings tree. The renderer uses this path to display errors related to this specific object (e.g., "transform not found") in the correct location.
    *   `settings: BaseSettings`: A cached copy of the object's settings (e.g., `visible`, `color`). This is a performance optimization to avoid repeatedly querying the main settings tree every frame for every object.
    *   `topic?: string`: The message topic that generated this object, used for organization and grouping within extensions.

## The Update and Rendering Loop: From Message to Pixels

1.  **Message Ingestion**: Messages from subscribed topics are queued for the relevant `SceneExtension`.
2.  **Animation Frame Callback**: The browser’s `requestAnimationFrame` invokes the `Renderer`’s main `animationFrame` method.
3.  **Extension Update**: The `Renderer` iterates through all `SceneExtension`s and calls their `startFrame` method.
4.  **Renderable Update**: The base `SceneExtension#startFrame` method iterates through its managed `renderables`. For each one, it performs the following:
    *   Checks `renderable.userData.settings.visible`. If not visible, it is skipped.
    *   Calls the `updatePose` function.
5.  **`updatePose`: The Core of Transformation**: This function is where the object's final position is determined.
    *   It determines the correct timestamp for the lookup: `currentTime` if `frameLocked`, or `messageTime` if not.
    *   It calls `renderer.transformTree.lookupTransform(...)`, providing the object's `frameId`, the camera's `renderFrameId`, the `fixedFrameId`, and the chosen timestamp.
    *   The `TransformTree` performs a lookup, interpolating between known transforms if necessary, to find the exact transformation matrix.
    *   This matrix is then applied to the `renderable`’s `position` and `quaternion` properties, placing it correctly in the scene.
    *   If the lookup fails, an error is added to the settings UI via the `settingsPath`.

## How Child Objects are Updated

This concept relies on the fundamentals of hierarchical transformation in 3D graphics, as provided by THREE.js.

*   **Mechanism**: A `Renderable` is a `THREE.Object3D`, which acts as a node in the scene graph. You can add other `THREE.Object3D`s as children to it (`parentRenderable.add(childObject)`). These children have their own local position and rotation *relative to their parent*. The final world position of a child is calculated by multiplying the parent's world transformation matrix by the child's local transformation matrix.

*   **Rationale**: The `SceneExtension` code **never needs to manually update the children of a `Renderable`**. When `updatePose` modifies the position and rotation of a parent `Renderable`, it is updating that parent's world transformation matrix. Because the children's final transforms are mathematically dependent on the parent's, they are automatically and instantly repositioned correctly by the underlying THREE.js engine. This design is highly efficient, as an extension can create a complex object with many parts and only needs to update the single, top-level `Renderable`.

## Guide: Creating Complex Renderables with Children

To create complex objects, you often need to nest `Renderable`s or `THREE.Object3D`s. There are two primary models for this within the framework, exemplified by the `Grids` and `Urdfs` extensions.

### Model 1: Compositional Hierarchy (The `Grids` Model)

This model is best when your main `Renderable` acts as a "manager" that contains one or more complex, self-contained "worker" children. The parent doesn't render anything itself; it delegates the rendering work.

**When to use it**:
*   When you want to reuse existing, complex `Renderable` types (e.g., `RenderableLineList`, `RenderablePointCloud`).
*   When the child components have their own sophisticated rendering logic that you don't want to replicate.
*   When the overall object is a composition of a few, distinct parts.

**How to implement it**:
1.  **Create a Parent `Renderable`**: This will be your main container. It will hold the overall settings and data for the composite object.
2.  **Instantiate Child `Renderable`s**: In the parent's constructor or an update method, create instances of other `Renderable` types (e.g., `new RenderableLineList(...)`).
3.  **Establish the Hierarchy**: Add the child `Renderable`s to the parent using `parent.add(child)`.
4.  **Delegate Updates**: The parent `Renderable` should be responsible for passing data to its children. In the `Grids` example, the `Grids` extension updates the `GridRenderable`, which then creates a `Marker` message and calls `child.update(marker)` on its `RenderableLineList` child.
5.  **Transform Handling**: The parent's pose is updated by the `SceneExtension`'s `startFrame` loop. Since the child is in the parent's scene graph subtree, it will automatically move with the parent.

```typescript
// --- Conceptual Example: A Renderable for a car with wheels ---

// The main container Renderable
class CarRenderable extends Renderable {
  private body: RenderableMeshResource;
  private wheels: RenderableMeshResource[] = [];

  public constructor(name: string, renderer: IRenderer, userData: CarUserData) {
    super(name, renderer, userData);

    // Create the body as a child Renderable
    this.body = new RenderableMeshResource(/*...*/);
    this.add(this.body);

    // Create the wheels as child Renderables
    for (let i = 0; i < 4; i++) {
      const wheel = new RenderableMeshResource(/*...*/);
      // Position the wheel relative to the car body's origin
      wheel.position.set(/* x, y, z */);
      this.wheels.push(wheel);
      this.add(wheel);
    }
  }

  // A method to update the children based on new data
  public updateCarData(data: CarMessage): void {
    // Update the body (if needed)
    this.body.update(/*...*/);

    // Update wheel rotations
    this.wheels.forEach((wheel, i) => {
      wheel.rotation.y = data.wheel_rotation[i];
    });
  }
}
```

### Model 2: Direct Child Manipulation (The `Urdfs` Model)

This model is best for highly articulated objects where each part has its own unique coordinate frame and needs to be positioned independently by the `TransformTree`.

**When to use it**:
*   For articulated objects like robot arms or characters, where parts are defined by a tree of joints and links.
*   When each child part has its own `frameId` and its pose is determined by the global `TransformTree`, not by its parent's pose in the local scene graph.

**How to implement it**:
1.  **Create a Parent `Renderable`**: This acts as a simple container or group for all the parts. It typically sits at the origin of its frame.
2.  **Instantiate Child `Renderable`s**: For each part (e.g., a URDF link), create a lightweight `Renderable` (like `RenderableCube` or `RenderableMeshResource`).
3.  **Crucial Step**: Populate the `userData` of *each child* with its specific `frameId`.
4.  **Establish Hierarchy**: Add all child `Renderable`s to the single parent `Renderable`.
5.  **Override `startFrame`**: The `SceneExtension` must override the default `startFrame` behavior. Instead of calling `updatePose` on the parent, it must iterate through the parent's children (`parent.userData.renderables.values()`) and call `updatePose` on *each child individually*. This ensures each child looks up its own transform in the `TransformTree` based on its unique `frameId`.

```typescript
// --- Conceptual Example: A SceneExtension for a robot arm ---

class RobotArmExtension extends SceneExtension<UrdfRenderable> {

  // In the method that processes the robot description...
  private loadRobot(robotDescription: RobotDescription): void {
    const robotRenderable = new UrdfRenderable(/*...*/);
    this.add(robotRenderable);

    // For each link in the description...
    for (const link of robotDescription.links) {
      const linkRenderable = new RenderableMeshResource(/*...*/);

      // Set the child's userData with the specific frameId for that link
      linkRenderable.userData.frameId = link.frame_id;

      // Add the link to the main robot renderable
      robotRenderable.add(linkRenderable);
      // Also keep a reference for the startFrame loop
      robotRenderable.userData.renderables.set(link.name, linkRenderable);
    }
  }

  // Override startFrame to update each child individually
  public override startFrame(
    currentTime: bigint,
    renderFrameId: AnyFrameId,
    fixedFrameId: AnyFrameId,
  ): void {
    for (const robot of this.renderables.values()) {
      if (!robot.visible) continue;

      // Iterate through the children (the links) and update each one
      for (const link of robot.userData.renderables.values()) {
        updatePose(
          link,
          this.renderer.transformTree,
          renderFrameId,
          fixedFrameId,
          link.userData.frameId, // Use the child's frameId
          currentTime,
          currentTime,
        );
      }
    }
  }
}
```

### Advanced Guide: Hybrid Hierarchies (The `PlanningScene` Model)

Some scenarios, like rendering a `moveit_msgs/PlanningScene`, require a three-layer (or deeper) hierarchy that combines both models.

A `PlanningScene` contains `CollisionObject`s. Each `CollisionObject` has its own `header.frame_id` and a main `pose`. It also contains an array of primitives (like meshes and boxes) with their own poses *relative to the `CollisionObject`'s main pose*. This requires a hybrid approach.

**The Hierarchy**:
1.  **`PlanningSceneExtension`**: The top-level `SceneExtension`. It manages a collection of `CollisionObjectRenderable`s.
2.  **`CollisionObjectRenderable`**: A `Renderable` representing a single collision object. It has its own `frameId` and is updated using the **Direct Child Manipulation** model.
3.  **`RenderableMeshResource`/`RenderableCube`**: The actual visual shapes. These are children of a `CollisionObjectRenderable` and are updated using the **Compositional Hierarchy** model.

**The Chain of Transformations**

To render a mesh from a `CollisionObject` correctly, the system must apply a chain of three transformations:

1.  **Mesh Pose**: The `mesh_poses[i]` transforms the mesh from its local model space into the `CollisionObject`'s coordinate space.
2.  **Object Pose**: The main `pose` of the `CollisionObject` transforms the result from the `CollisionObject`'s space into the coordinate space defined by its `header.frame_id`.
3.  **TF Tree Transform**: The `TransformTree` then transforms the result from the `header.frame_id` space into the final render frame (the camera's frame).

The framework handles this automatically through the scene graph as long as you set up the hierarchy correctly.

**How to implement it**:
1.  **Create the `PlanningSceneExtension`**:
    *   This class subscribes to the `/planning_scene` topic.
    *   When it receives a message, it will create, update, or remove `CollisionObjectRenderable` instances.
    *   It **must override `startFrame`** to iterate through its `CollisionObjectRenderable` children and call `updatePose` on each one individually (Direct Child Manipulation).

2.  **Create the `CollisionObjectRenderable`**:
    *   This class acts as a container. It does not render anything itself.
    *   When created or updated, it must receive the `CollisionObject` message.
    *   **Crucially, you must set its `userData` from the message**: `this.userData.frameId = object.header.frame_id` and `this.userData.pose = object.pose`. The `updatePose` function in the extension will use this data to apply the Object Pose and TF Tree Transform.
    *   It will have an `update(collisionObject)` method to manage its children.
    *   Inside `update()`, it creates the actual shape `Renderable`s (e.g., `RenderableMeshResource`) as its children. It sets the `position` and `quaternion` of these shape children based on the *local* poses from the `primitive_poses` or `mesh_poses` arrays. This applies the Mesh Pose.

3.  **The Update Flow**:
    *   The `PlanningSceneExtension`'s `startFrame` loop calls `updatePose` on a `CollisionObjectRenderable`.
    *   The `updatePose` function applies the combined TF Tree Transform and the Object Pose (from `userData.pose`) to the `CollisionObjectRenderable`.
    *   Because the shape `Renderable`s are children of the `CollisionObjectRenderable`, the THREE.js scene graph automatically multiplies their local Mesh Pose transform with their parent's newly calculated world transform. The full chain is applied correctly.

```typescript
// --- Conceptual Example: A PlanningScene Hybrid Model ---

// Layer 3: The actual shapes. These are standard renderables.
// (e.g., RenderableMeshResource, RenderableCube)

// Layer 2: The container for a single collision object.
class CollisionObjectRenderable extends Renderable {
  private shapes = new Map<string, Renderable>();

  public constructor(name: string, renderer: IRenderer, userData: CollisionObjectUserData) {
    super(name, renderer, userData);
  }

  public update(object: CollisionObject): void {
    // Apply the main pose of the CollisionObject to this container's userData
    this.userData.pose = object.pose;

    this.clearShapes();

    for (let i = 0; i < object.meshes.length; i++) {
      const mesh = object.meshes[i];
      const pose = object.mesh_poses[i];

      const shape = new RenderableMeshResource(/*...*/);
      // Set the LOCAL pose of the shape relative to this container
      shape.position.set(pose.position.x, pose.position.y, pose.position.z);
      shape.quaternion.set(pose.orientation.x, /*...*/);

      this.add(shape);
      this.shapes.set(`mesh_${i}`, shape);
    }
    // ... and so on for primitives ...
  }

  private clearShapes(): void { /* ... */ }
}

// Layer 1: The main SceneExtension
class PlanningSceneExtension extends SceneExtension<CollisionObjectRenderable> {

  private handleSceneMessage(msg: PlanningScene): void {
    for (const co of msg.world.collision_objects) {
      let renderable = this.renderables.get(co.id);
      if (!renderable) {
        renderable = new CollisionObjectRenderable(/*...*/);
        // Set the frameId from the collision object's header
        renderable.userData.frameId = co.header.frame_id;
        this.renderables.set(co.id, renderable);
        this.add(renderable);
      }
      // Update the container. This will set its main pose and update its children.
      renderable.update(co);
    }
  }

  public override startFrame(
    currentTime: bigint,
    renderFrameId: AnyFrameId,
    fixedFrameId: AnyFrameId,
  ): void {
    for (const coRenderable of this.renderables.values()) {
      if (!coRenderable.visible) continue;

      updatePose(
        coRenderable, // Update the container
        this.renderer.transformTree,
        renderFrameId,
        fixedFrameId,
        coRenderable.userData.frameId, // Use the container's frameId
        currentTime,
        currentTime,
      );
    }
  }
}
```