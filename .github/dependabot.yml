version: 2
updates:
  - package-ecosystem: npm
    directory: "/"
    schedule:
      # We found daily updates to be too noisy. They consumed too much CI time and engineer review
      # time relative to the benefit of being on such a bleeding edge.
      interval: monthly
    open-pull-requests-limit: 10
    labels: [] # disable default labels

    # Define groups of dependencies to be updated together
    # https://github.blog/changelog/2023-06-30-grouped-version-updates-for-dependabot-public-beta/
    groups:
      storybook:
        patterns:
          - "storybook"
          - "@storybook/*"
      eslint:
        patterns:
          - "@typescript-eslint/*"
      emotion:
        patterns:
          - "@emotion/*"
      electron:
        patterns:
          - "electron"
          - "electron-*"
          - "app-builder-lib"
          - "builder-util"
      jest:
        patterns:
          - "jest"
          - "jest-*"
          - "babel-jest"
          - "@types/jest"
      webpack:
        patterns:
          - "webpack"
          - "webpack-cli"
          - "webpack-dev-server"
      babel:
        patterns:
          - "@babel/*"
      monaco-editor:
        patterns:
          - "monaco-editor"
          - "monaco-editor-webpack-plugin"
          - "react-monaco-editor"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"
    labels: [] # disable default labels
