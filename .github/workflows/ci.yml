name: CI

on:
  push:
    branches: [main, release/*]
  pull_request:

jobs:
  test:
    runs-on: ${{ matrix.os }}

    # Run each command in parallel with the same setup steps.
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
        config:
          # Specify names so that the GitHub branch protection settings for
          # required checks don't need to change if we ever change the commands used.
          - name: lint
            command: |
              # lint steps
              set -x
              yarn license-check
              yarn dedupe --check
              yarn run tsc --noEmit # typecheck files that are not included by webpack or package builds
              yarn run lint:ci
              yarn run lint:unused-exports
              yarn run lint:dependencies
          - name: packages
            command: yarn run build:packages
          - name: web
            command: yarn run web:build:prod
          - name: desktop
            command: yarn run desktop:build:prod
          - name: build benchmark
            command: yarn run benchmark:build:prod
          - name: test
            command: yarn run test --maxWorkers=100%
        include:
          - os: windows-latest
            config:
              name: packages
              command: echo complete

    name: ${{ matrix.config.name }} (${{ matrix.os }})

    steps:
      - uses: actions/checkout@v4.1.1

      - uses: actions/setup-node@v4.0.3
        with:
          node-version: 20
      - run: corepack enable yarn

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            **/node_modules
          key: v5-${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: v5-${{ runner.os }}-yarn-

      - run: yarn install --immutable

      - run: ${{ matrix.config.command }}

  e2e:
    runs-on: ubuntu-latest

    name: e2e (ubuntu-latest)

    # Set a fixed timezone to ensure consistent test results.
    # Lichtblick uses the system's local timezone, which can cause test assertions
    # to fail due to time discrepancies across different environments.
    env:
      TZ: Europe/Lisbon

    steps:
      - uses: actions/checkout@v4.1.1

      - uses: actions/setup-node@v4.0.3
        with:
          node-version: 20
      - run: corepack enable yarn

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            **/node_modules
          key: v5-${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: v5-${{ runner.os }}-yarn-

      - run: yarn install --immutable

      - name: Apply AppArmor Fix (Ubuntu)
        # Required for Playwright on Ubuntu to work properly
        # See: https://github.com/microsoft/playwright/issues/34251
        run: sudo sysctl -w kernel.apparmor_restrict_unprivileged_userns=0

      - name: test:e2e:desktop:ci (ubuntu-latest)
        uses: coactions/setup-xvfb@v1
        with:
          working-directory: ./
          run: |
            yarn desktop:build:prod
            yarn test:e2e:desktop:ci
