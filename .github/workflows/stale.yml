name: <PERSON><PERSON>

on:
  schedule:
    - cron: "0 0 * * *"

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9.1.0
        with:
          stale-issue-message: "This issue has been marked as stale because there has been no activity in the past 12 months. Please add a comment to keep it open."
          stale-issue-label: stale
          days-before-issue-stale: 365
          days-before-issue-close: 30

          stale-pr-message: "This PR has been marked as stale because there has been no activity in the past 3 months. Please add a comment to keep it open."
          stale-pr-label: stale
          days-before-pr-stale: 90
          days-before-pr-close: 30
          delete-branch: true
