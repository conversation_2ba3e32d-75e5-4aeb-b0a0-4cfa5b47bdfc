name: SonarCloud

on:
  push:
    branches: ["main"]
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch:

jobs:
  build:
    name: SonarCloud
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Set up Node.js
        uses: actions/setup-node@v4.0.3
        with:
          node-version: 20

      - name: Enable corepack
        run: corepack enable yarn

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            **/node_modules
          key: v5-${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: v5-${{ runner.os }}-yarn-

      - name: Install dependencies
        run: yarn install

      - name: Generates lint report and file path formatting
        run: |
          yarn lint:ci:report
          sed -i 's|/home/<USER>/work/lichtblick/lichtblick/|./|g' eslint-report.json
        continue-on-error: true

      - name: Test and coverage
        run: yarn test:coverage
        continue-on-error: true

      - name: ScanCloud Scan
        uses: sonarsource/sonarqube-scan-action@v5.3.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
