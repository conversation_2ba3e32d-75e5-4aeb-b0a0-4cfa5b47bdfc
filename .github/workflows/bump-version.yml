name: Auto Bump Version

on:
  push:
    branches:
      - main # Trigger on push to main branch

permissions:
  actions: write

jobs:
  bump:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.LICHTBLICK_GITHUB_TOKEN }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "14"

      - run: corepack enable

      - name: Cache yarn dependencies
        uses: actions/cache@v4
        with:
          path: |
            **/node_modules
            ~/.cache/yarn
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install dependencies
        run: yarn install --mode skip-build
        env:
          YARN_ENABLE_IMMUTABLE_INSTALLS: false

      - name: Set up Git
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'

      - name: Bump version in root package.json
        run: yarn version patch

      - name: Bump version in packages/suite
        working-directory: packages/suite
        run: yarn version patch

      - name: Commit version bumps
        id: commit_version_bumps
        run: |
          git add package.json yarn.lock packages/suite/package.json
          git commit -m "chore: bump versions in root and suite package.json [skip actions]"
          echo "commit_sha=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Push changes
        run: |
          git push origin main

      - name: Trigger pre-build workflow
        run: |
          curl -X POST -H "Accept: application/vnd.github+json" \
               -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
               -H "X-GitHub-Api-Version: 2022-11-28" \
               -d '{"ref":"main"}' \
               https://api.github.com/repos/${{ github.repository }}/actions/workflows/pre-build.yml/dispatches
