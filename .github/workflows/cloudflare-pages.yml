name: <PERSON><PERSON>lar<PERSON> Pages

on:
  pull_request: {}

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  deploy:
    name: Deploy to Cloudflare Pages
    runs-on: ubuntu-latest

    env:
      # Environment variables that are injected into the build
      ENABLE_EXPERIMENTAL_COREPACK: 1

    steps:
      - uses: actions/checkout@v4.1.1
        with:
          submodules: true

      - uses: actions/setup-node@v4.0.3
        with:
          node-version: 16.17

      - run: corepack enable

      - run: yarn install --immutable

      - run: yarn web:build:prod

      - name: vercel-cloudflare compatibility
        run: |
          cp web/.cloudflare/* web/.webpack/

      - uses: cloudflare/pages-action@v1.5.0
        with:
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          gitHubToken: ${{ secrets.LICHTBLICKBOT_GITHUB_TOKEN }}
          branch: ${{ github.head_ref || github.ref_name }}
          projectName: lichtblick-suite-oss
          directory: web/.webpack
