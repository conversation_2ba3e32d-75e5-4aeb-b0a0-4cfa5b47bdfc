name: Post Release

# run after releases are published
on:
  release:
    types: [released]

jobs:
  npm:
    name: Publish to NPM
    runs-on: ubuntu-latest

    steps:
      - name: Checkout ${{ github.event.release.tag_name }}
        uses: actions/checkout@v4.1.1
        with:
          ref: ${{ github.event.release.tag_name }}

      - uses: actions/setup-node@v4.0.3
        with:
          node-version: 16.17
          registry-url: https://registry.npmjs.org

      - run: corepack enable yarn

      - run: yarn install --immutable

      - run: npm publish ./packages/suite
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_PUBLISH_TOKEN }}

  docker:
    name: Publish to GHCR
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout ${{ github.event.release.tag_name }}
        uses: actions/checkout@v4.1.1
        with:
          ref: ${{ github.event.release.tag_name }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Format version
        id: format_version
        run: |
          VERSION=${{ github.event.release.tag_name }}
          FORMATTED_VERSION=$(echo "$VERSION" | sed 's/^v//')
          echo "::set-output name=formatted_version::$FORMATTED_VERSION"

      - name: Build and Push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:latest
            ghcr.io/${{ github.repository }}:${{ steps.format_version.outputs.formatted_version }}
