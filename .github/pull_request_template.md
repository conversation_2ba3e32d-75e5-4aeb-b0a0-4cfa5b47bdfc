## User-Facing Changes

<!-- will be used as a changelog entry -->

## Description

<!-- link relevant GitHub issues -->
<!-- add `docs` label if this PR requires documentation updates -->
<!-- add relevant metric tracking for experimental / new features -->

## Checklist

- [ ] The web version was tested and it is running ok
- [ ] The desktop version was tested and it is running ok
- [ ] This change is covered by unit tests
- [ ] Files constants.ts, types.ts and *.style.ts have been checked and relevant code snippets have been relocated
